package com.myzone.controller;

import com.myzone.common.Result;
import com.myzone.dto.LoginRequest;
import com.myzone.entity.User;
import com.myzone.service.UserService;
import com.myzone.utils.BeanConverter;
import com.myzone.vo.LoginResponse;
import com.myzone.vo.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 认证控制器
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Tag(name = "认证管理", description = "用户登录认证相关接口")
public class AuthController {
    
    private final UserService userService;
    
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户名密码登录，返回JWT Token")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        // 执行登录
        String token = userService.login(request.getUsername(), request.getPassword());
        
        // 获取用户信息
        User user = userService.getByUsername(request.getUsername());
        UserVO userVO = BeanConverter.toUserVO(user);
        
        // 构造响应
        LoginResponse response = new LoginResponse(token, userVO);
        
        log.info("用户登录成功: {}", request.getUsername());
        return Result.success("登录成功", response);
    }
    
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "根据Token获取当前登录用户的详细信息")
    public Result<UserVO> getCurrentUser(Authentication authentication) {
        Long userId = (Long) authentication.getPrincipal();
        User user = userService.getById(userId);
        UserVO userVO = BeanConverter.toUserVO(user);
        
        return Result.success(userVO);
    }
    
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出（客户端删除Token即可）")
    public Result<Void> logout() {
        // JWT是无状态的，登出只需要客户端删除Token
        return Result.success("登出成功");
    }
}