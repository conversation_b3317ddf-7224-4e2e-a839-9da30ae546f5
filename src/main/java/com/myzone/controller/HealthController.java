package com.myzone.controller;

import com.myzone.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2025-07-23
 */
@RestController
@RequestMapping("/health")
@Tag(name = "系统健康检查", description = "系统状态检查相关接口")
public class HealthController {
    
    @GetMapping
    @Operation(summary = "健康检查", description = "检查系统是否正常运行")
    public Result<Map<String, Object>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("timestamp", LocalDateTime.now());
        healthInfo.put("application", "MyZone Personal Space");
        healthInfo.put("version", "1.0.0");
        
        return Result.success("系统运行正常", healthInfo);
    }
    
    @GetMapping("/info")
    @Operation(summary = "系统信息", description = "获取系统基本信息")
    public Result<Map<String, Object>> info() {
        Map<String, Object> systemInfo = new HashMap<>();
        systemInfo.put("application", "MyZone Personal Space Backend");
        systemInfo.put("version", "1.0.0");
        systemInfo.put("author", "Claude 4.0 sonnet");
        systemInfo.put("description", "个人空间后端系统，支持短文、长文、图片、视频发布");
        systemInfo.put("features", new String[]{
            "内容管理", "标签系统", "评论回复", "内容引用", "文件上传", "搜索功能"
        });
        
        return Result.success(systemInfo);
    }
}
