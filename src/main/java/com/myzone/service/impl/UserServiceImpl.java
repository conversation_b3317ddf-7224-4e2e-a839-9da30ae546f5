package com.myzone.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.myzone.common.BusinessException;
import com.myzone.common.ResultCode;
import com.myzone.entity.User;
import com.myzone.mapper.UserMapper;
import com.myzone.service.UserService;
import com.myzone.utils.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * 用户服务实现类
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2025-07-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    
    @Override
    public User getByUsername(String username) {
        return this.getOne(new LambdaQueryWrapper<User>()
                .eq(User::getUsername, username));
    }
    
    @Override
    public String login(String username, String password) {
        // 查询用户
        User user = getByUsername(username);
        if (user == null) {
            throw new BusinessException(ResultCode.LOGIN_FAILED, "用户名或密码错误");
        }
        
        // 验证密码
        log.info("验证用户 {} 的密码: 输入的密码长度={}, 数据库中的密码={}", username, password.length(), user.getPassword());
        if (!passwordEncoder.matches(password, user.getPassword())) {
            log.warn("用户 {} 密码验证失败: 输入的密码={}, 数据库中的密码={}", username, password, user.getPassword());
            throw new BusinessException(ResultCode.LOGIN_FAILED, "用户名或密码错误");
        }
        
        // 生成JWT Token
        return jwtUtil.generateToken(user.getId(), user.getUsername());
    }
    
    @Override
    public boolean updateUserInfo(Long userId, String nickname, String bio, String avatar) {
        User user = new User();
        user.setId(userId);
        user.setNickname(nickname);
        user.setBio(bio);
        user.setAvatar(avatar);
        
        return this.updateById(user);
    }
    
    @Override
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        // 查询用户
        User user = this.getById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.NOT_FOUND, "用户不存在");
        }
        
        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "原密码错误");
        }
        
        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        return this.updateById(user);
    }
}
