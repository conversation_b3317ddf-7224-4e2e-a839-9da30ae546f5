package com.myzone.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import com.myzone.common.BusinessException;
import com.myzone.common.ResultCode;
import com.myzone.config.MinioConfig;
import com.myzone.entity.MediaFile;
import com.myzone.mapper.MediaFileMapper;
import com.myzone.service.FileService;
import io.minio.*;
import io.minio.http.Method;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 文件服务实现类
 * 
 * <AUTHOR> 4.0 sonnet
 * @since 2025-07-23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileServiceImpl implements FileService {
    
    private final MinioClient minioClient;
    private final MinioConfig minioConfig;
    private final MediaFileMapper mediaFileMapper;
    
    @Value("${myzone.file.allowed-types}")
    private String allowedTypes;
    
    @Value("${myzone.file.max-size}")
    private Long maxSize;
    
    // 支持的图片格式
    private static final List<String> IMAGE_TYPES = Arrays.asList("jpg", "jpeg", "png", "gif", "webp");
    // 支持的视频格式
    private static final List<String> VIDEO_TYPES = Arrays.asList("mp4", "avi", "mov", "wmv", "flv", "webm");
    
    @Override
    public MediaFile uploadFile(MultipartFile file, Long userId) {
        // 参数校验
        if (file == null || file.isEmpty()) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "文件不能为空");
        }
        
        // 文件大小校验
        if (file.getSize() > maxSize) {
            throw new BusinessException(ResultCode.FILE_SIZE_ERROR);
        }
        
        // 文件类型校验
        String originalFilename = file.getOriginalFilename();
        String fileExtension = FileUtil.extName(originalFilename).toLowerCase();
        if (!Arrays.asList(allowedTypes.split(",")).contains(fileExtension)) {
            throw new BusinessException(ResultCode.FILE_TYPE_ERROR);
        }
        
        try {
            // 确保bucket存在
            ensureBucketExists();
            
            // 生成文件路径
            String fileName = generateFileName(fileExtension);
            String filePath = generateFilePath(fileName);
            
            // 上传文件到MinIO
            minioClient.putObject(
                PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(filePath)
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build()
            );
            
            // 创建媒体文件记录
            MediaFile mediaFile = new MediaFile();
            mediaFile.setUserId(userId);
            mediaFile.setOriginalName(originalFilename);
            mediaFile.setFileName(fileName);
            mediaFile.setFilePath(filePath);
            mediaFile.setFileSize(file.getSize());
            mediaFile.setFileType(fileExtension);
            mediaFile.setMimeType(file.getContentType());
            
            // 处理图片信息
            if (IMAGE_TYPES.contains(fileExtension)) {
                processImageInfo(file, mediaFile);
                // 生成缩略图
                generateThumbnail(file, mediaFile);
            }
            
            // 保存到数据库
            mediaFileMapper.insert(mediaFile);
            
            log.info("文件上传成功: {}", filePath);
            return mediaFile;
            
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new BusinessException(ResultCode.FILE_UPLOAD_ERROR, "文件上传失败: " + e.getMessage());
        }
    }
    
    @Override
    public List<MediaFile> uploadFiles(List<MultipartFile> files, Long userId) {
        List<MediaFile> result = new ArrayList<>();
        for (MultipartFile file : files) {
            result.add(uploadFile(file, userId));
        }
        return result;
    }
    
    @Override
    public boolean deleteFile(Long fileId, Long userId) {
        MediaFile mediaFile = mediaFileMapper.selectById(fileId);
        if (mediaFile == null) {
            throw new BusinessException(ResultCode.FILE_NOT_FOUND);
        }
        
        // 权限检查
        if (!mediaFile.getUserId().equals(userId)) {
            throw new BusinessException(ResultCode.FORBIDDEN, "无权限删除此文件");
        }
        
        try {
            // 从MinIO删除文件
            minioClient.removeObject(
                RemoveObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(mediaFile.getFilePath())
                    .build()
            );
            
            // 删除缩略图
            if (mediaFile.getThumbnailPath() != null) {
                minioClient.removeObject(
                    RemoveObjectArgs.builder()
                        .bucket(minioConfig.getBucketName())
                        .object(mediaFile.getThumbnailPath())
                        .build()
                );
            }
            
            // 从数据库删除记录
            return mediaFileMapper.deleteById(fileId) > 0;
            
        } catch (Exception e) {
            log.error("删除文件失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "删除文件失败");
        }
    }
    
    @Override
    public String getFileUrl(String filePath) {
        try {
            return minioClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(minioConfig.getBucketName())
                    .object(filePath)
                    .expiry(60 * 60 * 24) // 24小时有效期
                    .build()
            );
        } catch (Exception e) {
            log.error("获取文件URL失败", e);
            return null;
        }
    }
    
    @Override
    public List<MediaFile> getUserMediaFiles(Long userId, String fileType) {
        if (fileType != null) {
            return mediaFileMapper.selectByFileType(fileType);
        }
        return mediaFileMapper.selectByUserId(userId);
    }
    
    /**
     * 确保bucket存在
     */
    private void ensureBucketExists() throws Exception {
        boolean exists = minioClient.bucketExists(
            BucketExistsArgs.builder()
                .bucket(minioConfig.getBucketName())
                .build()
        );
        
        if (!exists) {
            minioClient.makeBucket(
                MakeBucketArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .build()
            );
        }
    }
    
    /**
     * 生成文件名
     */
    private String generateFileName(String extension) {
        return IdUtil.simpleUUID() + "." + extension;
    }
    
    /**
     * 生成文件路径
     */
    private String generateFilePath(String fileName) {
        String datePath = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        return datePath + "/" + fileName;
    }
    
    /**
     * 处理图片信息
     */
    private void processImageInfo(MultipartFile file, MediaFile mediaFile) {
        try (InputStream inputStream = file.getInputStream()) {
            BufferedImage image = ImageIO.read(inputStream);
            if (image != null) {
                mediaFile.setWidth(image.getWidth());
                mediaFile.setHeight(image.getHeight());
            }
        } catch (Exception e) {
            log.warn("读取图片信息失败", e);
        }
    }
    
    /**
     * 生成缩略图
     */
    private void generateThumbnail(MultipartFile file, MediaFile mediaFile) {
        try (InputStream inputStream = file.getInputStream()) {
            // 生成缩略图
            ByteArrayOutputStream thumbnailOutput = new ByteArrayOutputStream();
            Thumbnails.of(inputStream)
                    .size(300, 300)
                    .keepAspectRatio(true)
                    .toOutputStream(thumbnailOutput);
            
            // 上传缩略图
            String thumbnailPath = "thumbnails/" + mediaFile.getFilePath();
            minioClient.putObject(
                PutObjectArgs.builder()
                    .bucket(minioConfig.getBucketName())
                    .object(thumbnailPath)
                    .stream(new ByteArrayInputStream(thumbnailOutput.toByteArray()), 
                           thumbnailOutput.size(), -1)
                    .contentType("image/jpeg")
                    .build()
            );
            
            mediaFile.setThumbnailPath(thumbnailPath);
            
        } catch (Exception e) {
            log.warn("生成缩略图失败", e);
        }
    }
}
